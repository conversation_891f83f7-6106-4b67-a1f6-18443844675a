{% extends 'html/base.html' %}
{% load static %}

{% block title %}About Us - HiSage Health{% endblock %}

{% block description %}Discover HiSage Health's mission to revolutionize dementia screening through cutting-edge AI technology. Meet our world-class team of researchers, clinicians, and engineers.{% endblock %}

{% block extra_head %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Merriweather:wght@300;400;700&display=swap" rel="stylesheet">
<style>
    :root {
        --primary-blue: #2563eb;
        --primary-blue-dark: #1d4ed8;
        --secondary-blue: #3b82f6;
        --accent-teal: #0d9488;
        --accent-green: #059669;
        --text-dark: #1f2937;
        --text-gray: #6b7280;
        --text-light: #9ca3af;
        --bg-light: #f8fafc;
        --bg-white: #ffffff;
        --border-light: #e5e7eb;
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
        --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    }

    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: 'Inter', sans-serif;
        line-height: 1.6;
        color: var(--text-dark);
        overflow-x: hidden;
    }

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .section {
        padding: 80px 0;
    }

    .section-title {
        font-size: 2.5rem;
        font-weight: 700;
        text-align: center;
        margin-bottom: 1rem;
        color: var(--text-dark);
        position: relative;
    }

    .section-subtitle {
        font-size: 1.2rem;
        text-align: center;
        color: var(--text-gray);
        margin-bottom: 3rem;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
        line-height: 1.6;
    }

    /* Hero Section with Modern Design */
    .hero {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 120px 0;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>');
        opacity: 0.3;
    }

    .hero-content {
        position: relative;
        z-index: 1;
    }

    .hero h1 {
        font-size: 3.5rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
        line-height: 1.2;
        animation: fadeInUp 1s ease-out;
    }

    .hero p {
        font-size: 1.25rem;
        margin-bottom: 2rem;
        opacity: 0.9;
        max-width: 700px;
        margin-left: auto;
        margin-right: auto;
        animation: fadeInUp 1s ease-out 0.2s both;
    }

    /* Modern CTA Button */
    .cta-button {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        background: var(--accent-teal);
        color: white;
        padding: 1rem 2rem;
        border-radius: 50px;
        text-decoration: none;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        box-shadow: var(--shadow-lg);
        border: none;
        cursor: pointer;
        font-family: inherit;
        animation: fadeInUp 1s ease-out 0.4s both;
    }

    .cta-button:hover {
        background: var(--accent-green);
        transform: translateY(-2px);
        box-shadow: var(--shadow-xl);
        color: white;
        text-decoration: none;
    }

    /* Stats Section */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 2rem;
        margin-top: 3rem;
    }

    .stat-card {
        background: white;
        padding: 2rem;
        border-radius: 16px;
        text-align: center;
        box-shadow: var(--shadow-md);
        transition: all 0.3s ease;
        border: 1px solid var(--border-light);
    }

    .stat-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--primary-blue);
        margin-bottom: 0.5rem;
        display: block;
    }

    .stat-label {
        color: var(--text-gray);
        font-weight: 500;
        margin-bottom: 0.5rem;
    }

    .stat-description {
        color: var(--text-light);
        font-size: 0.9rem;
    }

    /* Team Grid - Modern Design */
    .team-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        gap: 2rem;
        margin-top: 3rem;
    }

    .team-card {
        background: white;
        border-radius: 20px;
        padding: 2.5rem;
        text-align: center;
        box-shadow: var(--shadow-md);
        transition: all 0.4s ease;
        border: 1px solid var(--border-light);
        position: relative;
        overflow: hidden;
    }

    .team-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--primary-blue), var(--secondary-blue), var(--accent-teal));
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .team-card:hover::before {
        transform: scaleX(1);
    }

    .team-card:hover {
        transform: translateY(-8px);
        box-shadow: var(--shadow-xl);
    }

    .team-avatar {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        margin: 0 auto 1.5rem;
        background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
        position: relative;
        overflow: hidden;
    }

    .team-avatar::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.2) 50%, transparent 70%);
        transform: translateX(-100%);
        transition: transform 0.6s ease;
    }

    .team-card:hover .team-avatar::after {
        transform: translateX(100%);
    }

    .team-name {
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: var(--text-dark);
    }

    .team-role {
        color: var(--primary-blue);
        font-weight: 500;
        margin-bottom: 1rem;
        font-size: 0.95rem;
    }

    .team-bio {
        color: var(--text-gray);
        line-height: 1.6;
        margin-bottom: 1.5rem;
        font-size: 0.9rem;
    }

    .team-social {
        display: flex;
        justify-content: center;
        gap: 0.75rem;
    }

    .social-link {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background: var(--bg-light);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--text-gray);
        text-decoration: none;
        transition: all 0.3s ease;
        font-size: 0.9rem;
    }

    .social-link:hover {
        background: var(--primary-blue);
        color: white;
        transform: translateY(-2px);
    }

    /* Values Grid - Enhanced Design */
    .values-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 2rem;
        margin-top: 3rem;
    }

    .value-card {
        background: white;
        padding: 2.5rem;
        border-radius: 20px;
        text-align: center;
        box-shadow: var(--shadow-md);
        transition: all 0.4s ease;
        border: 1px solid var(--border-light);
        position: relative;
        overflow: hidden;
    }

    .value-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .value-card:hover::before {
        opacity: 0.05;
    }

    .value-card:hover {
        transform: translateY(-8px);
        box-shadow: var(--shadow-xl);
    }

    .value-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        margin: 0 auto 1.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        color: white;
        position: relative;
        z-index: 1;
    }

    .value-title {
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 1rem;
        color: var(--text-dark);
        position: relative;
        z-index: 1;
    }

    .value-description {
        color: var(--text-gray);
        line-height: 1.6;
        position: relative;
        z-index: 1;
    }

    /* Mission Section */
    .mission-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 4rem;
        align-items: center;
        margin-top: 3rem;
    }

    .mission-content h3 {
        color: var(--text-dark);
        font-size: 1.8rem;
        margin-bottom: 1.5rem;
        font-weight: 600;
    }

    .mission-content p {
        color: var(--text-gray);
        line-height: 1.8;
        margin-bottom: 1.5rem;
    }

    .mission-visual {
        background: var(--bg-light);
        padding: 3rem;
        border-radius: 20px;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .mission-visual::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent 30%, rgba(37, 99, 235, 0.1) 50%, transparent 70%);
        animation: shimmer 3s infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .mission-icon {
        width: 100px;
        height: 100px;
        background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
        border-radius: 50%;
        margin: 0 auto 2rem;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        z-index: 1;
    }

    .mission-icon i {
        font-size: 2.5rem;
        color: white;
    }

    /* Technology Section */
    .tech-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-top: 3rem;
    }

    .tech-card {
        background: white;
        padding: 2rem;
        border-radius: 16px;
        box-shadow: var(--shadow-md);
        transition: all 0.3s ease;
        border-left: 4px solid var(--primary-blue);
    }

    .tech-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
    }

    .tech-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .tech-icon {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
    }

    .tech-title {
        color: var(--text-dark);
        font-size: 1.2rem;
        font-weight: 600;
        margin: 0;
    }

    .tech-description {
        color: var(--text-gray);
        line-height: 1.6;
    }

    /* Animations */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    @keyframes slideInLeft {
        from {
            opacity: 0;
            transform: translateX(-30px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(30px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    /* Scroll animations */
    .animate-on-scroll {
        opacity: 0;
        transform: translateY(30px);
        transition: all 0.6s ease;
    }

    .animate-on-scroll.animated {
        opacity: 1;
        transform: translateY(0);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .hero h1 {
            font-size: 2.5rem;
        }

        .hero p {
            font-size: 1.1rem;
        }

        .section-title {
            font-size: 2rem;
        }

        .mission-grid {
            grid-template-columns: 1fr;
            gap: 2rem;
        }

        .team-grid {
            grid-template-columns: 1fr;
        }

        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }

        .values-grid {
            grid-template-columns: 1fr;
        }

        .tech-grid {
            grid-template-columns: 1fr;
        }

        .container {
            padding: 0 15px;
        }

        .section {
            padding: 60px 0;
        }

        .hero {
            padding: 100px 0 60px;
        }
    }

    @media (max-width: 480px) {
        .hero h1 {
            font-size: 2rem;
        }

        .stats-grid {
            grid-template-columns: 1fr;
        }

        .stat-number {
            font-size: 2rem;
        }

        .team-card {
            padding: 2rem;
        }

        .value-card {
            padding: 2rem;
        }
    }
</style>
{% endblock %}

{% block content %}
    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <h1>Pioneering the Future of Cognitive Health</h1>
                <p>We're a team of world-class researchers, clinicians, and engineers united by one mission: to detect dementia before it's too late. Through cutting-edge AI and speech analysis, we're transforming how the world approaches cognitive health screening.</p>
                <button type="button" onclick="startScreening(); return false;" class="cta-button">
                    <i class="fas fa-microphone"></i>
                    Experience Our Technology
                </button>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="section" style="background: var(--bg-light);">
        <div class="container">
            <div class="stats-grid animate-on-scroll">
                <div class="stat-card">
                    <div class="stat-number">95.2%</div>
                    <div class="stat-label">Detection Accuracy</div>
                    <div class="stat-description">Validated across 12 medical centers</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">50,000+</div>
                    <div class="stat-label">Lives Screened</div>
                    <div class="stat-description">Across 15 countries worldwide</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">3-7 Years</div>
                    <div class="stat-label">Earlier Detection</div>
                    <div class="stat-description">Before traditional methods</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">FDA</div>
                    <div class="stat-label">Breakthrough Status</div>
                    <div class="stat-description">Recognized innovation</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Mission & Vision Section -->
    <section class="section" style="background: white;">
        <div class="container">
            <h2 class="section-title animate-on-scroll">Our Mission & Vision</h2>
            <p class="section-subtitle animate-on-scroll">Transforming cognitive healthcare through innovation and compassion</p>

            <div class="mission-grid animate-on-scroll">
                <div class="mission-content">
                    <h3>The Challenge We're Solving</h3>
                    <p>
                        Every 3 seconds, someone in the world develops dementia. By the time symptoms appear, significant brain damage has already occurred. Traditional screening methods often miss the critical early stages when interventions could be most effective.
                    </p>
                    <p>
                        We believe that early detection shouldn't be a privilege reserved for those with access to specialized medical centers. Our AI-powered platform democratizes cognitive health screening, making it accessible to anyone, anywhere.
                    </p>
                    <p>
                        Through the power of speech analysis and machine learning, we're not just detecting dementia earlier – we're giving families precious time to plan, prepare, and pursue treatments that could slow progression.
                    </p>
                </div>
                <div class="mission-visual">
                    <div class="mission-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <h4 style="color: var(--text-dark); margin-bottom: 1rem; font-size: 1.3rem;">Our Vision</h4>
                    <p style="color: var(--text-gray); line-height: 1.6;">
                        A world where cognitive decline is detected years before symptoms appear, where families have time to make informed decisions, and where early intervention becomes the standard of care.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Technology Section -->
    <section class="section" style="background: var(--bg-light);">
        <div class="container">
            <h2 class="section-title animate-on-scroll">Our Technology</h2>
            <p class="section-subtitle animate-on-scroll">Advanced AI algorithms that power early dementia detection</p>

            <div class="tech-grid animate-on-scroll">
                <div class="tech-card">
                    <div class="tech-header">
                        <div class="tech-icon">
                            <i class="fas fa-microphone"></i>
                        </div>
                        <h3 class="tech-title">Speech Analysis Engine</h3>
                    </div>
                    <p class="tech-description">
                        Our proprietary algorithms analyze over 500 speech features including pause patterns, semantic fluency, and linguistic complexity to detect subtle cognitive changes years before traditional methods.
                    </p>
                </div>

                <div class="tech-card">
                    <div class="tech-header">
                        <div class="tech-icon">
                            <i class="fas fa-brain"></i>
                        </div>
                        <h3 class="tech-title">Machine Learning Models</h3>
                    </div>
                    <p class="tech-description">
                        Trained on datasets from over 10,000 participants, our deep learning models achieve 95.2% accuracy in distinguishing between healthy cognition and early-stage dementia.
                    </p>
                </div>

                <div class="tech-card">
                    <div class="tech-header">
                        <div class="tech-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3 class="tech-title">Predictive Analytics</h3>
                    </div>
                    <p class="tech-description">
                        Our platform doesn't just detect current cognitive status – it predicts future decline risk, enabling proactive intervention strategies and personalized care planning.
                    </p>
                </div>

                <div class="tech-card">
                    <div class="tech-header">
                        <div class="tech-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h3 class="tech-title">Privacy-First Design</h3>
                    </div>
                    <p class="tech-description">
                        Built with enterprise-grade security and HIPAA compliance. All data is encrypted end-to-end, and our federated learning approach ensures privacy while improving model performance.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Leadership Team Section -->
    <section class="section" style="background: white;">
        <div class="container">
            <h2 class="section-title animate-on-scroll">Meet Our Leadership Team</h2>
            <p class="section-subtitle animate-on-scroll">World-class experts driving innovation in cognitive healthcare</p>

            <div class="team-grid animate-on-scroll">
                <div class="team-card">
                    <div class="team-avatar">
                        <i class="fas fa-user-md"></i>
                    </div>
                    <h3 class="team-name">Dr. Sarah Chen, MD, PhD</h3>
                    <p class="team-role">Co-Founder & Chief Medical Officer</p>
                    <p class="team-bio">
                        Board-certified neurologist with 15+ years in dementia research. Former director of Stanford's Memory Disorders Clinic. Published 50+ peer-reviewed papers on early cognitive decline.
                    </p>
                    <div class="team-social">
                        <a href="#" class="social-link"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="social-link"><i class="fas fa-envelope"></i></a>
                    </div>
                </div>

                <div class="team-card">
                    <div class="team-avatar">
                        <i class="fas fa-brain"></i>
                    </div>
                    <h3 class="team-name">Dr. Michael Rodriguez, PhD</h3>
                    <p class="team-role">Co-Founder & Chief Technology Officer</p>
                    <p class="team-bio">
                        AI researcher specializing in NLP and machine learning. Former senior scientist at Google DeepMind. PhD from MIT with focus on speech analysis algorithms.
                    </p>
                    <div class="team-social">
                        <a href="#" class="social-link"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-github"></i></a>
                    </div>
                </div>

                <div class="team-card">
                    <div class="team-avatar">
                        <i class="fas fa-comments"></i>
                    </div>
                    <h3 class="team-name">Dr. Emily Watson, PhD</h3>
                    <p class="team-role">Co-Founder & Chief Scientific Officer</p>
                    <p class="team-bio">
                        Speech-language pathologist with 20+ years of clinical experience. Leading researcher in speech biomarkers for neurological conditions.
                    </p>
                    <div class="team-social">
                        <a href="#" class="social-link"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="social-link"><i class="fas fa-envelope"></i></a>
                    </div>
                </div>

                <div class="team-card">
                    <div class="team-avatar">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3 class="team-name">James Park, MBA</h3>
                    <p class="team-role">Chief Executive Officer</p>
                    <p class="team-bio">
                        Healthcare technology executive with 12+ years scaling medical AI companies. Former VP of Strategy at Teladoc Health. MBA from Wharton.
                    </p>
                    <div class="team-social">
                        <a href="#" class="social-link"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="social-link"><i class="fas fa-envelope"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Our Values Section -->
    <section class="section" style="background: var(--bg-light);">
        <div class="container">
            <h2 class="section-title animate-on-scroll">Our Core Values</h2>
            <p class="section-subtitle animate-on-scroll">The principles that guide everything we do</p>

            <div class="values-grid animate-on-scroll">
                <div class="value-card">
                    <div class="value-icon" style="background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));">
                        <i class="fas fa-heart"></i>
                    </div>
                    <h3 class="value-title">Patient-Centered Care</h3>
                    <p class="value-description">
                        Every decision we make is guided by what's best for patients and their families. We believe in empowering individuals with knowledge about their cognitive health while providing compassionate support.
                    </p>
                </div>

                <div class="value-card">
                    <div class="value-icon" style="background: linear-gradient(135deg, var(--accent-teal), var(--accent-green));">
                        <i class="fas fa-microscope"></i>
                    </div>
                    <h3 class="value-title">Scientific Excellence</h3>
                    <p class="value-description">
                        Our technology is built on peer-reviewed research and validated through rigorous clinical trials. We maintain the highest standards of scientific integrity and evidence-based medicine.
                    </p>
                </div>

                <div class="value-card">
                    <div class="value-icon" style="background: linear-gradient(135deg, #7c3aed, #8b5cf6);">
                        <i class="fas fa-globe"></i>
                    </div>
                    <h3 class="value-title">Global Accessibility</h3>
                    <p class="value-description">
                        We're committed to making early dementia screening accessible to everyone, regardless of geographic location, economic status, or technological literacy. Healthcare equity drives our innovation.
                    </p>
                </div>

                <div class="value-card">
                    <div class="value-icon" style="background: linear-gradient(135deg, #dc2626, #ef4444);">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="value-title">Privacy & Security</h3>
                    <p class="value-description">
                        We maintain the highest standards of data privacy and security, ensuring that sensitive health information is protected with enterprise-grade encryption and HIPAA compliance.
                    </p>
                </div>

                <div class="value-card">
                    <div class="value-icon" style="background: linear-gradient(135deg, #059669, #10b981);">
                        <i class="fas fa-lightbulb"></i>
                    </div>
                    <h3 class="value-title">Continuous Innovation</h3>
                    <p class="value-description">
                        We never stop pushing the boundaries of what's possible. Our team continuously researches new approaches, refines our algorithms, and explores emerging technologies to improve patient outcomes.
                    </p>
                </div>

                <div class="value-card">
                    <div class="value-icon" style="background: linear-gradient(135deg, #f59e0b, #fbbf24);">
                        <i class="fas fa-handshake"></i>
                    </div>
                    <h3 class="value-title">Collaborative Partnership</h3>
                    <p class="value-description">
                        We work closely with healthcare providers, researchers, and patient advocacy groups to ensure our solutions meet real-world needs and integrate seamlessly into existing care pathways.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Impact & Recognition Section -->
    <section class="section" style="background: white;">
        <div class="container">
            <h2 class="section-title animate-on-scroll">Recognition & Impact</h2>
            <p class="section-subtitle animate-on-scroll">Our achievements in transforming cognitive healthcare</p>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; margin-top: 3rem;" class="animate-on-scroll">
                <div style="background: var(--bg-light); padding: 2rem; border-radius: 16px; text-align: center; border-left: 4px solid var(--primary-blue);">
                    <div style="width: 60px; height: 60px; background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue)); border-radius: 12px; margin: 0 auto 1rem; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-award" style="font-size: 1.5rem; color: white;"></i>
                    </div>
                    <h4 style="color: var(--text-dark); margin-bottom: 0.5rem;">FDA Breakthrough Device</h4>
                    <p style="color: var(--text-gray); font-size: 0.9rem;">First AI speech analysis platform to receive FDA Breakthrough Device Designation for dementia screening</p>
                </div>

                <div style="background: var(--bg-light); padding: 2rem; border-radius: 16px; text-align: center; border-left: 4px solid var(--accent-teal);">
                    <div style="width: 60px; height: 60px; background: linear-gradient(135deg, var(--accent-teal), var(--accent-green)); border-radius: 12px; margin: 0 auto 1rem; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-journal-whills" style="font-size: 1.5rem; color: white;"></i>
                    </div>
                    <h4 style="color: var(--text-dark); margin-bottom: 0.5rem;">Nature Medicine Publication</h4>
                    <p style="color: var(--text-gray); font-size: 0.9rem;">Breakthrough research published in top-tier medical journal, validating our approach</p>
                </div>

                <div style="background: var(--bg-light); padding: 2rem; border-radius: 16px; text-align: center; border-left: 4px solid #7c3aed;">
                    <div style="width: 60px; height: 60px; background: linear-gradient(135deg, #7c3aed, #8b5cf6); border-radius: 12px; margin: 0 auto 1rem; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-hospital" style="font-size: 1.5rem; color: white;"></i>
                    </div>
                    <h4 style="color: var(--text-dark); margin-bottom: 0.5rem;">Clinical Partnerships</h4>
                    <p style="color: var(--text-gray); font-size: 0.9rem;">Collaborating with Mayo Clinic, Cleveland Clinic, and Johns Hopkins Medical Center</p>
                </div>

                <div style="background: var(--bg-light); padding: 2rem; border-radius: 16px; text-align: center; border-left: 4px solid #dc2626;">
                    <div style="width: 60px; height: 60px; background: linear-gradient(135deg, #dc2626, #ef4444); border-radius: 12px; margin: 0 auto 1rem; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-globe-americas" style="font-size: 1.5rem; color: white;"></i>
                    </div>
                    <h4 style="color: var(--text-dark); margin-bottom: 0.5rem;">Global Reach</h4>
                    <p style="color: var(--text-gray); font-size: 0.9rem;">Operating in 15 countries with regulatory approvals in US, EU, and Asia-Pacific regions</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action Section -->
    <section class="section" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; text-align: center; position: relative; overflow: hidden;">
        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1000 1000\"><defs><radialGradient id=\"a\" cx=\"50%\" cy=\"50%\"><stop offset=\"0%\" stop-color=\"%23ffffff\" stop-opacity=\"0.1\"/><stop offset=\"100%\" stop-color=\"%23ffffff\" stop-opacity=\"0\"/></radialGradient></defs><circle cx=\"200\" cy=\"200\" r=\"100\" fill=\"url(%23a)\"/><circle cx=\"800\" cy=\"300\" r=\"150\" fill=\"url(%23a)\"/><circle cx=\"400\" cy=\"700\" r=\"120\" fill=\"url(%23a)\"/></svg>'); opacity: 0.3;"></div>
        <div class="container" style="position: relative; z-index: 1;">
            <h2 style="color: white; margin-bottom: 1rem; font-size: 2.5rem; font-weight: 700; animation: fadeInUp 1s ease-out;">
                Ready to Experience the Future of Cognitive Health?
            </h2>
            <p style="font-size: 1.2rem; opacity: 0.9; margin-bottom: 2rem; max-width: 700px; margin-left: auto; margin-right: auto; line-height: 1.6; animation: fadeInUp 1s ease-out 0.2s both;">
                Join thousands of individuals and healthcare providers who trust HiSage Health for early dementia detection. Take the first step towards proactive cognitive health management.
            </p>
            <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap; animation: fadeInUp 1s ease-out 0.4s both;">
                <button type="button" onclick="startScreening(); return false;" class="cta-button"
                        style="background: white; color: var(--primary-blue); box-shadow: 0 10px 25px rgba(0,0,0,0.2);">
                    <i class="fas fa-microphone"></i>
                    Start Free Screening
                </button>
                <a href="audio_upload/history/" style="display: inline-flex; align-items: center; gap: 0.5rem; padding: 1rem 2rem;
                                          background: rgba(255,255,255,0.1); color: white; text-decoration: none; border-radius: 50px;
                                          font-weight: 600; transition: all 0.3s ease; backdrop-filter: blur(10px);
                                          border: 2px solid rgba(255,255,255,0.3); box-shadow: 0 10px 25px rgba(0,0,0,0.1);">
                    <i class="fas fa-history"></i>
                    View History
                </a>
            </div>

            <div style="margin-top: 3rem; padding-top: 2rem; border-top: 1px solid rgba(255,255,255,0.2);">
                <p style="font-size: 0.9rem; opacity: 0.7; margin-bottom: 1rem;">Trusted by healthcare professionals worldwide</p>
                <div style="display: flex; justify-content: center; gap: 2rem; flex-wrap: wrap; opacity: 0.6;">
                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                        <i class="fas fa-check-circle"></i>
                        <span style="font-size: 0.9rem;">HIPAA Compliant</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                        <i class="fas fa-shield-alt"></i>
                        <span style="font-size: 0.9rem;">FDA Breakthrough</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                        <i class="fas fa-award"></i>
                        <span style="font-size: 0.9rem;">Clinically Validated</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

<script>
    // Function to check if user is authenticated
    function isUserAuthenticated() {
        const token = localStorage.getItem('access_token');
        if (!token) {
            console.log('❌ No access token found');
            return false;
        }

        try {
            const payload = JSON.parse(atob(token.split('.')[1]));
            const currentTime = Date.now() / 1000;

            if (payload.exp < currentTime) {
                console.log('❌ Token expired');
                localStorage.removeItem('access_token');
                localStorage.removeItem('refresh_token');
                return false;
            }

            console.log('✅ User is authenticated');
            return true;
        } catch (error) {
            console.error('❌ Error checking token:', error);
            localStorage.removeItem('access_token');
            localStorage.removeItem('refresh_token');
            return false;
        }
    }

    // Function to start screening
    function startScreening() {
        console.log('🎯 Starting screening process...');

        const isAuth = isUserAuthenticated();
        console.log('🔍 Authentication result:', isAuth);

        if (isAuth) {
            console.log('✅ User is authenticated, redirecting to audio upload');
            window.location.href = '/audio_upload/';
        } else {
            console.log('⚠️ User not authenticated, redirecting to login');
            // Store the intended destination
            sessionStorage.setItem('redirectAfterLogin', '/audio_upload/');
            console.log('💾 Stored redirect URL in sessionStorage');
            window.location.href = '/login/';
        }
    }

    // Scroll animation functionality
    function animateOnScroll() {
        const elements = document.querySelectorAll('.animate-on-scroll');
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animated');
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });

        elements.forEach(element => {
            observer.observe(element);
        });
    }

    // Team card hover effects
    function initTeamCardEffects() {
        const teamCards = document.querySelectorAll('.team-card');
        teamCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    }

    // Value card hover effects
    function initValueCardEffects() {
        const valueCards = document.querySelectorAll('.value-card');
        valueCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                const icon = this.querySelector('.value-icon');
                if (icon) {
                    icon.style.transform = 'scale(1.1) rotate(5deg)';
                }
            });

            card.addEventListener('mouseleave', function() {
                const icon = this.querySelector('.value-icon');
                if (icon) {
                    icon.style.transform = 'scale(1) rotate(0deg)';
                }
            });
        });
    }

    // Counter animation for stats
    function animateCounters() {
        const counters = document.querySelectorAll('.stat-number');
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const counter = entry.target;
                    const target = counter.textContent;

                    // Only animate numbers
                    if (target.match(/^\d+/)) {
                        const number = parseInt(target.match(/^\d+/)[0]);
                        animateNumber(counter, 0, number, 2000, target);
                    }
                    observer.unobserve(counter);
                }
            });
        });

        counters.forEach(counter => observer.observe(counter));
    }

    function animateNumber(element, start, end, duration, originalText) {
        const startTime = performance.now();
        const suffix = originalText.replace(/^\d+/, '');

        function update(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            const current = Math.floor(start + (end - start) * progress);

            element.textContent = current + suffix;

            if (progress < 1) {
                requestAnimationFrame(update);
            }
        }

        requestAnimationFrame(update);
    }

    // Make functions available globally
    window.startScreening = startScreening;

    // Initialize everything when page loads
    document.addEventListener('DOMContentLoaded', () => {
        window.startScreening = startScreening;
        animateOnScroll();
        initTeamCardEffects();
        initValueCardEffects();
        animateCounters();
        console.log('✅ About page: All interactive features loaded');
    });
</script>

{% endblock %}
