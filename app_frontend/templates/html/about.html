{% extends 'html/base.html' %}
{% load static %}

{% block title %}About Us - HiSage Health{% endblock %}

{% block description %}Meet the team behind HiSage Health and learn about our journey in revolutionizing dementia screening through AI technology.{% endblock %}

{% block extra_head %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<style>
    :root {
        --primary-blue: #2563eb;
        --secondary-blue: #3b82f6;
        --accent-teal: #0d9488;
        --accent-green: #059669;
        --text-dark: #1f2937;
        --text-gray: #6b7280;
        --text-light: #9ca3af;
        --bg-light: #f8fafc;
        --bg-white: #ffffff;
        --border-light: #e5e7eb;
    }

    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: 'Inter', sans-serif;
        line-height: 1.6;
        color: var(--text-dark);
    }

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .section {
        padding: 80px 0;
    }

    .section-title {
        font-size: 2.5rem;
        font-weight: 700;
        text-align: center;
        margin-bottom: 1rem;
        color: var(--text-dark);
    }

    .section-subtitle {
        font-size: 1.2rem;
        text-align: center;
        color: var(--text-gray);
        margin-bottom: 3rem;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    /* Hero Section */
    .hero {
        background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
        color: white;
        padding: 120px 0 80px;
        text-align: center;
    }

    .hero h1 {
        font-size: 3.5rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
    }

    .hero p {
        font-size: 1.3rem;
        opacity: 0.9;
        max-width: 700px;
        margin: 0 auto;
    }

    /* Team Member Card */
    .team-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-top: 3rem;
    }

    .team-card {
        background: white;
        border-radius: 16px;
        padding: 2rem;
        text-align: center;
        box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        transition: all 0.3s ease;
        border: 1px solid var(--border-light);
    }

    .team-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1);
    }

    .team-avatar {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        margin: 0 auto 1.5rem;
        background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2.5rem;
    }

    .team-name {
        font-size: 1.4rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: var(--text-dark);
    }

    .team-role {
        color: var(--primary-blue);
        font-weight: 500;
        margin-bottom: 1rem;
    }

    .team-bio {
        color: var(--text-gray);
        line-height: 1.6;
        margin-bottom: 1.5rem;
    }

    .team-social {
        display: flex;
        justify-content: center;
        gap: 1rem;
    }

    .social-link {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: var(--bg-light);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--text-gray);
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .social-link:hover {
        background: var(--primary-blue);
        color: white;
    }

    /* Timeline */
    .timeline {
        position: relative;
        max-width: 800px;
        margin: 0 auto;
    }

    .timeline::before {
        content: '';
        position: absolute;
        left: 50%;
        top: 0;
        bottom: 0;
        width: 2px;
        background: var(--primary-blue);
        transform: translateX(-50%);
    }

    .timeline-item {
        position: relative;
        margin-bottom: 3rem;
        width: 50%;
    }

    .timeline-item:nth-child(odd) {
        left: 0;
        padding-right: 2rem;
    }

    .timeline-item:nth-child(even) {
        left: 50%;
        padding-left: 2rem;
    }

    .timeline-content {
        background: white;
        padding: 2rem;
        border-radius: 12px;
        box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        position: relative;
    }

    .timeline-date {
        background: var(--primary-blue);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: 600;
        display: inline-block;
        margin-bottom: 1rem;
    }

    .timeline-title {
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: var(--text-dark);
    }

    .timeline-description {
        color: var(--text-gray);
        line-height: 1.6;
    }

    /* Values Section */
    .values-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
        margin-top: 3rem;
    }

    .value-card {
        text-align: center;
        padding: 2rem;
    }

    .value-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        margin: 0 auto 1.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        color: white;
    }

    .value-title {
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 1rem;
        color: var(--text-dark);
    }

    .value-description {
        color: var(--text-gray);
        line-height: 1.6;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .hero h1 {
            font-size: 2.5rem;
        }
        
        .timeline::before {
            left: 20px;
        }
        
        .timeline-item {
            width: 100%;
            left: 0 !important;
            padding-left: 3rem !important;
            padding-right: 0 !important;
        }
        
        .team-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <h1>About HiSage Health</h1>
            <p>Meet the passionate team of researchers, clinicians, and engineers who are revolutionizing dementia screening through innovative AI technology. Our journey began with a simple question: How can we detect cognitive changes before they become irreversible?</p>
        </div>
    </section>

    <!-- Our Story Section -->
    <section class="section" style="background: white;">
        <div class="container">
            <h2 class="section-title">Our Story</h2>
            <p class="section-subtitle">From a research lab to a global healthcare solution</p>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 4rem; align-items: center; margin-bottom: 4rem;">
                <div>
                    <h3 style="color: var(--text-dark); font-size: 1.8rem; margin-bottom: 1.5rem;">The Beginning</h3>
                    <p style="color: var(--text-gray); line-height: 1.8; margin-bottom: 1.5rem;">
                        HiSage Health was founded in 2020 by Dr. Sarah Chen, a neurologist who witnessed the devastating impact of late-stage dementia diagnosis on countless families. Frustrated by the limitations of traditional cognitive assessments, she partnered with AI researcher Dr. Michael Rodriguez and speech pathologist Dr. Emily Watson.
                    </p>
                    <p style="color: var(--text-gray); line-height: 1.8; margin-bottom: 1.5rem;">
                        Our founders shared a vision: to harness the power of artificial intelligence and speech analysis to detect cognitive decline years before symptoms become apparent to families and doctors.
                    </p>
                    <p style="color: var(--text-gray); line-height: 1.8;">
                        What started as a research project in a small university lab has grown into a comprehensive platform that has screened over 50,000 individuals worldwide, helping families gain precious time for planning and intervention.
                    </p>
                </div>
                <div>
                    <div style="background: var(--bg-light); padding: 3rem; border-radius: 16px; text-align: center;">
                        <div style="width: 100px; height: 100px; background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue)); 
                                    border-radius: 50%; margin: 0 auto 2rem; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-lightbulb" style="font-size: 2.5rem; color: white;"></i>
                        </div>
                        <h4 style="color: var(--text-dark); margin-bottom: 1rem; font-size: 1.3rem;">Our Mission</h4>
                        <p style="color: var(--text-gray); line-height: 1.6;">
                            To democratize early dementia detection through cutting-edge AI technology, making cognitive health screening accessible, accurate, and affordable for everyone worldwide.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Leadership Team Section -->
    <section class="section" style="background: var(--bg-light);">
        <div class="container">
            <h2 class="section-title">Meet Our Leadership Team</h2>
            <p class="section-subtitle">Visionary leaders driving innovation in cognitive healthcare</p>

            <div class="team-grid">
                <div class="team-card">
                    <div class="team-avatar">
                        <i class="fas fa-user-md"></i>
                    </div>
                    <h3 class="team-name">Dr. Sarah Chen, MD, PhD</h3>
                    <p class="team-role">Co-Founder & Chief Medical Officer</p>
                    <p class="team-bio">
                        Board-certified neurologist with 15+ years of experience in dementia research. Former director of the Memory Disorders Clinic at Stanford Medical Center. Published 50+ peer-reviewed papers on early cognitive decline detection.
                    </p>
                    <div class="team-social">
                        <a href="#" class="social-link"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="social-link"><i class="fas fa-envelope"></i></a>
                    </div>
                </div>

                <div class="team-card">
                    <div class="team-avatar">
                        <i class="fas fa-brain"></i>
                    </div>
                    <h3 class="team-name">Dr. Michael Rodriguez, PhD</h3>
                    <p class="team-role">Co-Founder & Chief Technology Officer</p>
                    <p class="team-bio">
                        AI researcher specializing in natural language processing and machine learning. Former senior scientist at Google DeepMind. PhD in Computer Science from MIT with focus on speech analysis algorithms.
                    </p>
                    <div class="team-social">
                        <a href="#" class="social-link"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-github"></i></a>
                    </div>
                </div>

                <div class="team-card">
                    <div class="team-avatar">
                        <i class="fas fa-comments"></i>
                    </div>
                    <h3 class="team-name">Dr. Emily Watson, PhD</h3>
                    <p class="team-role">Co-Founder & Chief Scientific Officer</p>
                    <p class="team-bio">
                        Speech-language pathologist with expertise in cognitive-linguistic assessment. 20+ years of clinical experience. Leading researcher in speech biomarkers for neurological conditions.
                    </p>
                    <div class="team-social">
                        <a href="#" class="social-link"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="social-link"><i class="fas fa-envelope"></i></a>
                    </div>
                </div>

                <div class="team-card">
                    <div class="team-avatar">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3 class="team-name">James Park, MBA</h3>
                    <p class="team-role">Chief Executive Officer</p>
                    <p class="team-bio">
                        Healthcare technology executive with 12+ years of experience scaling medical AI companies. Former VP of Strategy at Teladoc Health. MBA from Wharton School with focus on healthcare innovation.
                    </p>
                    <div class="team-social">
                        <a href="#" class="social-link"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="social-link"><i class="fas fa-envelope"></i></a>
                    </div>
                </div>

                <div class="team-card">
                    <div class="team-avatar">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="team-name">Dr. Lisa Thompson, MD</h3>
                    <p class="team-role">Chief Regulatory Officer</p>
                    <p class="team-bio">
                        Former FDA medical officer with expertise in digital health regulations. 10+ years of experience in medical device approval processes. MD from Johns Hopkins with specialization in geriatric medicine.
                    </p>
                    <div class="team-social">
                        <a href="#" class="social-link"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="social-link"><i class="fas fa-envelope"></i></a>
                    </div>
                </div>

                <div class="team-card">
                    <div class="team-avatar">
                        <i class="fas fa-code"></i>
                    </div>
                    <h3 class="team-name">Alex Kumar, MS</h3>
                    <p class="team-role">VP of Engineering</p>
                    <p class="team-bio">
                        Full-stack engineer with expertise in scalable healthcare platforms. Former lead engineer at Epic Systems. MS in Computer Science from Carnegie Mellon with focus on healthcare informatics.
                    </p>
                    <div class="team-social">
                        <a href="#" class="social-link"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-github"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Company Timeline Section -->
    <section class="section" style="background: white;">
        <div class="container">
            <h2 class="section-title">Our Journey</h2>
            <p class="section-subtitle">Key milestones in our mission to transform dementia screening</p>

            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-content">
                        <div class="timeline-date">2020</div>
                        <h3 class="timeline-title">Company Founded</h3>
                        <p class="timeline-description">
                            Dr. Sarah Chen, Dr. Michael Rodriguez, and Dr. Emily Watson establish HiSage Health with initial seed funding from Stanford University's innovation fund. First prototype developed in university research lab.
                        </p>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-content">
                        <div class="timeline-date">2021</div>
                        <h3 class="timeline-title">Series A Funding & Team Expansion</h3>
                        <p class="timeline-description">
                            Raised $5M Series A led by Andreessen Horowitz. Expanded team to 15 employees including AI engineers, clinical researchers, and regulatory specialists. Moved to dedicated facility in Palo Alto.
                        </p>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-content">
                        <div class="timeline-date">2022</div>
                        <h3 class="timeline-title">Clinical Validation Studies</h3>
                        <p class="timeline-description">
                            Completed multi-site clinical trials with 2,500 participants across 12 medical centers. Published breakthrough research in Nature Medicine demonstrating 95.2% accuracy in early dementia detection.
                        </p>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-content">
                        <div class="timeline-date">2023</div>
                        <h3 class="timeline-title">FDA Breakthrough Designation</h3>
                        <p class="timeline-description">
                            Received FDA Breakthrough Device Designation for our AI-powered speech analysis platform. Initiated partnerships with Mayo Clinic, Cleveland Clinic, and Johns Hopkins for expanded clinical deployment.
                        </p>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-content">
                        <div class="timeline-date">2024</div>
                        <h3 class="timeline-title">Global Expansion</h3>
                        <p class="timeline-description">
                            Launched international operations in Europe and Asia. Established partnerships with healthcare systems in 15 countries. Reached milestone of 50,000+ individuals screened worldwide.
                        </p>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-content">
                        <div class="timeline-date">2025</div>
                        <h3 class="timeline-title">Next Generation Platform</h3>
                        <p class="timeline-description">
                            Launching HiSage 2.0 with enhanced multilingual support, real-time analysis, and integration with electronic health records. Preparing for Series B funding to accelerate global adoption.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Our Values Section -->
    <section class="section" style="background: var(--bg-light);">
        <div class="container">
            <h2 class="section-title">Our Core Values</h2>
            <p class="section-subtitle">The principles that guide everything we do</p>

            <div class="values-grid">
                <div class="value-card">
                    <div class="value-icon" style="background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));">
                        <i class="fas fa-heart"></i>
                    </div>
                    <h3 class="value-title">Patient-Centered</h3>
                    <p class="value-description">
                        Every decision we make is guided by what's best for patients and their families. We believe in empowering individuals with knowledge about their cognitive health.
                    </p>
                </div>

                <div class="value-card">
                    <div class="value-icon" style="background: linear-gradient(135deg, var(--accent-teal), var(--accent-green));">
                        <i class="fas fa-microscope"></i>
                    </div>
                    <h3 class="value-title">Scientific Rigor</h3>
                    <p class="value-description">
                        Our technology is built on peer-reviewed research and validated through rigorous clinical trials. We maintain the highest standards of scientific integrity.
                    </p>
                </div>

                <div class="value-card">
                    <div class="value-icon" style="background: linear-gradient(135deg, #7c3aed, #8b5cf6);">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 class="value-title">Accessibility</h3>
                    <p class="value-description">
                        We're committed to making early dementia screening accessible to everyone, regardless of geographic location, economic status, or technological literacy.
                    </p>
                </div>

                <div class="value-card">
                    <div class="value-icon" style="background: linear-gradient(135deg, #dc2626, #ef4444);">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="value-title">Privacy & Security</h3>
                    <p class="value-description">
                        We maintain the highest standards of data privacy and security, ensuring that sensitive health information is protected with enterprise-grade encryption.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action Section -->
    <section class="section" style="background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue)); color: white; text-align: center;">
        <div class="container">
            <h2 style="color: white; margin-bottom: 1rem; font-size: 2.5rem; font-weight: 700;">
                Join Us in Our Mission
            </h2>
            <p style="font-size: 1.2rem; opacity: 0.9; margin-bottom: 2rem; max-width: 600px; margin-left: auto; margin-right: auto; line-height: 1.6;">
                Whether you're a healthcare provider, researcher, or someone concerned about cognitive health, we invite you to be part of the solution.
            </p>
            <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                <button type="button" onclick="startScreening(); return false;"
                        style="background: white; color: var(--primary-blue); padding: 1rem 2rem; border: none;
                               border-radius: 50px; font-weight: 600; font-size: 1.1rem; cursor: pointer;
                               transition: all 0.3s ease; display: inline-flex; align-items: center; gap: 0.5rem;">
                    <i class="fas fa-microphone"></i>
                    Try Our Screening
                </button>
                <a href="/contact/" style="display: inline-flex; align-items: center; gap: 0.5rem; padding: 1rem 2rem;
                                          background: rgba(255,255,255,0.1); color: white; text-decoration: none; border-radius: 50px;
                                          font-weight: 600; transition: all 0.3s ease; backdrop-filter: blur(10px);
                                          border: 1px solid rgba(255,255,255,0.2);">
                    <i class="fas fa-envelope"></i>
                    Contact Us
                </a>
            </div>
        </div>
    </section>

<script>
    // Function to check if user is authenticated
    function isUserAuthenticated() {
        const token = localStorage.getItem('access_token');
        if (!token) {
            console.log('❌ No access token found');
            return false;
        }

        try {
            const payload = JSON.parse(atob(token.split('.')[1]));
            const currentTime = Date.now() / 1000;

            if (payload.exp < currentTime) {
                console.log('❌ Token expired');
                localStorage.removeItem('access_token');
                localStorage.removeItem('refresh_token');
                return false;
            }

            console.log('✅ User is authenticated');
            return true;
        } catch (error) {
            console.error('❌ Error checking token:', error);
            localStorage.removeItem('access_token');
            localStorage.removeItem('refresh_token');
            return false;
        }
    }

    // Function to start screening
    function startScreening() {
        console.log('🎯 Starting screening process...');

        const isAuth = isUserAuthenticated();
        console.log('🔍 Authentication result:', isAuth);

        if (isAuth) {
            console.log('✅ User is authenticated, redirecting to audio upload');
            window.location.href = '/audio_upload/';
        } else {
            console.log('⚠️ User not authenticated, redirecting to login');
            // Store the intended destination
            sessionStorage.setItem('redirectAfterLogin', '/audio_upload/');
            console.log('💾 Stored redirect URL in sessionStorage');
            window.location.href = '/login/';
        }
    }

    // Make function available globally
    window.startScreening = startScreening;

    // Ensure function is available when page loads
    document.addEventListener('DOMContentLoaded', () => {
        window.startScreening = startScreening;
        console.log('✅ About page: startScreening function loaded');
    });
</script>

{% endblock %}
